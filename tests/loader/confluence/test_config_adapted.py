#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires adaptés pour la configuration Confluence.

Adapté depuis src/kbotloadscheduler/loader/confluence/tests/test_config.py
pour l'architecture kbot-load-scheduler.
"""

import unittest

import pytest
from pydantic import ValidationError, SecretStr

from kbotloadscheduler.loader.confluence.config import ConfluenceConfig


class TestConfluenceConfigAdapted(unittest.TestCase):
    """Tests adaptés pour la classe ConfluenceConfig."""

    def setUp(self):
        """Configuration des tests."""
        self.base_config = {
            "url": "https://test.atlassian.net",
            "default_space_key": "TEST"
        }

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_with_pat_token(self):
        """Test de configuration avec PAT token."""
        config = ConfluenceConfig(
            **self.base_config,
            pat_token=SecretStr("test_pat_token")
        )

        assert str(config.url) == "https://test.atlassian.net/"  # URL normalisée avec /
        assert config.default_space_key == "TEST"
        assert config.pat_token.get_secret_value() == "test_pat_token"
        # Note: auth_type n'est pas un attribut direct de ConfluenceConfig

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_with_api_token(self):
        """Test de configuration avec API token."""
        config = ConfluenceConfig(
            **self.base_config,
            username="<EMAIL>",
            api_token="test_api_token"
        )

        assert config.username == "<EMAIL>"
        assert config.api_token.get_secret_value() == "test_api_token"
        # Note: auth_type n'est pas un attribut direct de ConfluenceConfig

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_validation_missing_url(self):
        """Test de validation avec URL manquante."""
        with pytest.raises(ValidationError):
            ConfluenceConfig(
                default_space_key="TEST",
                pat_token="test_token"
            )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_validation_invalid_url(self):
        """Test de validation avec URL invalide."""
        with pytest.raises(ValidationError):
            ConfluenceConfig(
                url="not-a-valid-url",
                default_space_key="TEST",
                pat_token="test_token"
            )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_validation_missing_auth(self):
        """Test de validation sans authentification."""
        with pytest.raises(ValidationError):
            ConfluenceConfig(
                url="https://test.atlassian.net",
                default_space_key="TEST"
            )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_validation_incomplete_api_token(self):
        """Test de validation avec API token incomplet."""
        with pytest.raises(ValidationError):
            ConfluenceConfig(
                **self.base_config,
                username="<EMAIL>"
                # api_token manquant
            )

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_timeout_default(self):
        """Test de la valeur par défaut du timeout."""
        config = ConfluenceConfig(
            **self.base_config,
            pat_token="test_token"
        )

        assert config.timeout == 30

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_timeout_custom(self):
        """Test de timeout personnalisé."""
        config = ConfluenceConfig(
            **self.base_config,
            pat_token="test_token",
            timeout=60
        )

        assert config.timeout == 60

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_parallel_pagination_default(self):
        """Test de la valeur par défaut de la pagination parallèle."""
        config = ConfluenceConfig(
            **self.base_config,
            pat_token="test_token"
        )

        assert config.enable_parallel_pagination is True
        assert config.max_parallel_requests == 3

    @pytest.mark.unit
    @pytest.mark.confluence
    def test_config_serialization(self):
        """Test de sérialisation de la configuration."""
        config = ConfluenceConfig(
            **self.base_config,
            pat_token="test_token",
            timeout=45
        )

        # Test que la configuration peut être sérialisée
        config_dict = config.model_dump()  # Utilisation de model_dump() au lieu de dict()
        assert "url" in config_dict
        assert "default_space_key" in config_dict
        assert "timeout" in config_dict

        # Les secrets ne doivent pas être exposés en clair
        assert config_dict.get("pat_token") != "test_token"


if __name__ == '__main__':
    unittest.main()
